<?php defined('BASEPATH') || exit('No direct script access allowed');

use App\Exceptions\FilterManagerException;

/**
 * Filter Manager Library
 *
 * Biblioteca reutilizável para gerenciar filtros em diferentes telas do sistema.
 * Baseada no padrão apply_default_filters do mestre_itens.php
 *
 * <AUTHOR>
 * @version 1.0
 * @package Libraries
 * @property CI $CI
 */
class Filter_Manager
{
    private $CI;
    private $model;
    private $input;

    /**
     * Configurações de filtros para cada tela
     * @var array
     */
    private $filter_configs = [];

    public function __construct()
    {
        $this->CI = &get_instance();
        $this->input = $this->CI->input;
    }

    /**
     * Inicializa o gerenciador de filtros para um modelo específico
     *
     * @param object $model Modelo que será usado para gerenciar os filtros
     * @return Filter_Manager
     */
    public function initialize($model)
    {
        $this->model = $model;
        return $this;
    }

    /**
     * Configura os filtros para uma tela específica
     *
     * @param array $config Configuração dos filtros
     * @return Filter_Manager
     */
    public function configure($config)
    {
        $this->filter_configs = array_merge($this->filter_configs, $config);
        return $this;
    }

    /**
     * Aplica filtros padrão baseado na configuração
     *
     * @param array|null $post Dados do POST
     * @return array
     */
    public function apply_default_filters($post = null)
    {
        if ($post === null) {
            $post = [];
        }
        ddJson($post);

        if (!$this->model) {
            throw new FilterManagerException('Model não foi inicializado. Use initialize() primeiro.');
        }

        $this->handle_filter_reset();

        // Aplicar filtros booleanos
        if (isset($this->filter_configs['boolean'])) {
            foreach ($this->filter_configs['boolean'] as $filter => $conditions) {
                $this->handle_boolean_filter($filter, $conditions);
            }
        }

        // Aplicar filtros de array
        if (isset($this->filter_configs['array'])) {
            foreach ($this->filter_configs['array'] as $filter => $conditions) {
                $this->handle_array_filter($filter, $post, $conditions);
            }
        }

        // Aplicar filtros simples
        if (isset($this->filter_configs['simple'])) {
            foreach ($this->filter_configs['simple'] as $filter => $conditions) {
                $this->handle_simple_filter($filter, $conditions);
            }
        }

        $this->handle_filtered_state();

        // Aplicar filtros customizados se existirem
        if (isset($this->filter_configs['custom'])) {
            foreach ($this->filter_configs['custom'] as $filter => $callback) {
                if (is_callable($callback)) {
                    $callback($this->model, $this->input, $post);
                }
            }
        }

        // Limpar filtros que não estão presentes no POST atual (quando filtered=1)
        $this->clear_absent_filters($post);

        return [];
    }

    /**
     * Gerencia o reset de filtros
     */
    public function handle_filter_reset()
    {
        if ($this->input->is_set('reset_filters')) {
            $this->model->set_state_store_session(true);
            $this->model->clear_states();

            // Após limpar, definir filtros essenciais que sempre devem existir
            $this->set_essential_filters();
        } else {
            $this->model->set_state_store_session(true);
            $this->model->restore_state_from_session('filter.', 'post');
        }
    }

    /**
     * Define filtros essenciais após reset
     */
    private function set_essential_filters()
    {
        // Definir empresa sempre
        $this->model->set_state('filter.id_empresa', sess_user_company());

        // Limpar qualquer resíduo de POST que possa causar problemas
        $this->clear_problematic_post_data();
    }

    /**
     * Limpa dados problemáticos do POST que podem causar erros de conversão
     */
    private function clear_problematic_post_data()
    {
        // Lista de campos que podem causar problemas se ficarem como array vazio
        $problematic_fields = [
            'pacotes_eventos',
            'sistema_origem',
            'owner',
            'prioridade',
            'status_classificacao_fiscal',
            'pre_agrupamento',
            'novo_material',
            'estabelecimento',
            'importado',
            'farol_sla'
        ];

        foreach ($problematic_fields as $field) {
            if (isset($_POST[$field])) {
                // Se é array vazio ou contém apenas strings vazias
                if (is_array($_POST[$field]) && (empty($_POST[$field]) || $_POST[$field] === array('') || $_POST[$field] === array(''))) {
                    unset($_POST[$field]);
                }
                // Se é string vazia
                elseif ($_POST[$field] === '') {
                    unset($_POST[$field]);
                }
                // Se é array com apenas valor -1 (que significa "todos")
                elseif (is_array($_POST[$field]) && count($_POST[$field]) === 1 && $_POST[$field][0] === '-1') {
                    unset($_POST[$field]);
                }
            }
        }
    }



    /**
     * Gerencia filtros booleanos (checkbox)
     *
     * @param string $filter Nome do filtro
     * @param array $conditions Condições para aplicar
     */
    private function handle_boolean_filter($filter, $conditions)
    {
        $is_form_submitted = $this->input->post('filtered') !== false;
        $post_value = $this->input->post($filter);

        // Para campos como triagem_diana_falha que têm input hidden + checkbox,
        // o POST pode vir como array [0, 1] quando marcado ou [0] quando desmarcado
        if (is_array($post_value) && count($post_value) > 1) {
            // Pegar o último valor (do checkbox, não do hidden)
            $post_value = end($post_value);
        } elseif (is_array($post_value) && count($post_value) === 1) {
            // Apenas o valor do hidden (checkbox desmarcado)
            $post_value = $post_value[0];
        }

        if ($is_form_submitted) {
            if ($this->check_conditions($post_value, $conditions)) {
                $this->model->set_state("filter.{$filter}", $post_value);
            } else {
                $this->model->unset_state("filter.{$filter}");
            }
        } else {
            $filter_value = $this->model->get_state("filter.{$filter}");
            if ($filter_value && $this->check_conditions($filter_value, $conditions)) {
                $this->model->set_state("filter.{$filter}", $filter_value);
            } else {
                $this->model->unset_state("filter.{$filter}");
            }
        }
    }

    /**
     * Gerencia filtros de array
     *
     * @param string $filter Nome do filtro
     * @param array $post Dados do POST
     * @param array $conditions Condições para aplicar
     */
    private function handle_array_filter($filter, $post, $conditions)
    {
        $filter_value = get_filter_value($filter, $this->model);

        // Verificações específicas para arrays problemáticos
        if ($filter === 'evento' && is_array($filter_value) && empty($filter_value[0])) {
            $this->model->unset_state("filter.{$filter}");
            return;
        }

        // Se o valor do filtro é null ou inválido, remover do estado
        if ($filter_value === null) {
            $this->model->unset_state("filter.{$filter}");
            return;
        }

        // Verificar se o valor é um array vazio ou contém apenas valores inválidos
        if (is_array($filter_value)) {
            $valid_values = array_filter($filter_value, function ($val) {
                return $val !== '' && $val !== null && $val !== '-1';
            });

            if (empty($valid_values)) {
                $this->model->unset_state("filter.{$filter}");
                return;
            }

            // Usar apenas valores válidos
            $filter_value = array_values($valid_values);
        }

        if (!empty($post[$filter]) && $this->check_conditions($filter_value, $conditions)) {
            $this->model->set_state("filter.{$filter}", $filter_value);
        } elseif (!$this->input->get('per_page') && $post && empty($post[$filter])) {
            $this->model->unset_state("filter.{$filter}");
        } elseif (isset($post[$filter]) && is_array($post[$filter]) && in_array(-1, $post[$filter])) {
            $this->model->unset_state("filter.{$filter}");
        } elseif (isset($post[$filter]) && $post[$filter] == -1) {
            $this->model->unset_state("filter.{$filter}");
        }
    }

    /**
     * Gerencia filtros simples
     *
     * @param string $filter Nome do filtro
     * @param array $conditions Condições para aplicar
     */
    private function handle_simple_filter($filter, $conditions)
    {
        $filter_value = get_filter_value($filter, $this->model);

        if ($filter_value === null) {
            if ($filter === 'item_input') {
                $this->model->unset_state('filter.search');
            } else {
                $this->model->unset_state("filter.{$filter}");
            }
        } else if ($this->check_conditions($filter_value, $conditions)) {
            if ($filter === 'item_input') {
                $this->model->set_state('filter.search', $filter_value);
            } else {
                $this->model->set_state("filter.{$filter}", $filter_value);
            }
        } else {
            if ($filter === 'item_input') {
                $this->model->unset_state('filter.search');
            } else {
                $this->model->unset_state("filter.{$filter}");
            }
        }
    }

    /**
     * Verifica condições para aplicar filtros
     *
     * @param mixed $value Valor do filtro
     * @param array $conditions Condições a verificar
     * @return bool
     */
    private function check_conditions($value, $conditions)
    {
        foreach ($conditions as $condition) {
            if (!$this->$condition($value)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Define o estado de filtrado
     */
    private function handle_filtered_state()
    {
        if ($this->input->is_set('filtered')) {
            $this->model->set_state('filter.filtered', $this->input->post('filtered'));
        } else {
            $this->model->set_state('filter.filtered', 0);
        }
    }

    /**
     * Limpa filtros que não estão presentes no POST atual
     * Isso garante que quando um usuário remove um filtro (deixa vazio),
     * ele seja removido da sessão também
     *
     * @param array $post Dados do POST atual
     */
    private function clear_absent_filters($post)
    {
        // Só fazer limpeza se for uma submissão de filtros (filtered=1)
        if (!$this->input->post('filtered')) {
            return;
        }



        // Lista de todos os filtros possíveis das configurações
        $all_filters = [];

        // Coletar filtros das configurações
        if (isset($this->filter_configs['boolean'])) {
            $all_filters = array_merge($all_filters, array_keys($this->filter_configs['boolean']));
        }

        if (isset($this->filter_configs['array'])) {
            $all_filters = array_merge($all_filters, array_keys($this->filter_configs['array']));
        }

        if (isset($this->filter_configs['simple'])) {
            $all_filters = array_merge($all_filters, array_keys($this->filter_configs['simple']));
        }

        // Para cada filtro configurado, verificar se não está presente no POST
        foreach ($all_filters as $filter) {
            $is_absent = !isset($post[$filter]);
            $is_empty = isset($post[$filter]) && $this->is_empty_filter_value($post[$filter]);

            // Se o filtro não está no POST ou está vazio/inválido
            if ($is_absent || $is_empty) {
                // Remove da sessão se estiver lá
                $this->model->unset_state("filter.{$filter}");
            }
        }

        // Filtros especiais que podem ter nomes diferentes
        $special_filters = [
            'data_criacao_from',
            'data_criacao_to',
            'data_modificacao_from',
            'data_modificacao_to',
            'data_importado_from',
            'data_importado_to',
        ];

        foreach ($special_filters as $filter) {
            if (!isset($post[$filter]) || $this->is_empty_filter_value($post[$filter])) {
                $this->model->unset_state("filter.{$filter}");
            }
        }
    }

    /**
     * Verifica se um valor de filtro está vazio ou é inválido
     *
     * @param mixed $value Valor a ser verificado
     * @return bool True se o valor está vazio/inválido
     */
    private function is_empty_filter_value($value)
    {
        // String vazia
        if ($value === '' || $value === null) {
            return true;
        }

        // Array vazio
        if (is_array($value) && empty($value)) {
            return true;
        }

        // Array com strings vazias
        if (is_array($value) && count($value) === 1 && $value[0] === '') {
            return true;
        }

        // Array com valor "todos" (-1)
        if (is_array($value) && count($value) === 1 && $value[0] === '-1') {
            return true;
        }

        // String com valor "todos" (-1)
        if ($value === '-1') {
            return true;
        }

        return false;
    }

    // ========== MÉTODOS DE VALIDAÇÃO ==========

    private function is_array($value)
    {
        return is_array($value);
    }

    private function not_contains_negative_one($value)
    {
        return !in_array(-1, $value);
    }

    private function is_not_empty($value)
    {
        return !empty($value);
    }

    private function not_equals_negative_one($value)
    {
        return $value != -1;
    }

    private function no_events($value)
    {
        return $value !== 'sem_evento';
    }

    private function is_checkbox_checked($value)
    {
        return $value == '1' || $value === 1 || $value === true;
    }

    /**
     * Adiciona uma condição customizada de validação
     *
     * @param string $name Nome da condição
     * @param callable $callback Função de validação
     */
    public function add_condition($name, $callback)
    {
        if (is_callable($callback)) {
            $this->$name = $callback;
        }
    }

    /**
     * Obtém configuração padrão para tela de dados técnicos
     *
     * @return array
     */
    public static function get_dados_tecnicos_config()
    {
        return [
            'boolean' => [
                'triagem_diana_falha' => ['is_checkbox_checked']
            ],
            'array' => [
                'pacotes_eventos' => ['is_not_empty', 'no_events'],
                'status_classificacao_fiscal' => ['is_not_empty'],
                'owner' => ['is_array', 'not_contains_negative_one'],
                'prioridade' => ['is_array', 'not_contains_negative_one'],
                'sistema_origem' => ['is_array', 'not_contains_negative_one'],
                'estabelecimento' => ['is_array', 'not_contains_negative_one'],
                'pre_agrupamento' => ['is_array', 'not_contains_negative_one'],
                'farol_sla' => ['is_array', 'not_contains_negative_one'],
            ],
            'simple' => [
                'item_input' => ['is_not_empty'],
                'search' => ['is_not_empty'],
                'atribuido_para' => ['is_not_empty', 'not_equals_negative_one'],
                'novo_material' => ['is_not_empty', 'not_equals_negative_one'],
                'importado' => ['is_not_empty', 'not_equals_negative_one'],
                'data_criacao_from' => ['is_not_empty'],
                'data_criacao_to' => ['is_not_empty'],
                'data_modificacao_from' => ['is_not_empty'],
                'data_modificacao_to' => ['is_not_empty'],
                'data_importado_from' => ['is_not_empty'],
                'data_importado_to' => ['is_not_empty']
            ]
        ];
    }

    /**
     * Obtém configuração para tela de mestre de itens
     *
     * @return array
     */
    public static function get_mestre_itens_config()
    {
        return [
            'boolean' => [
                'triagem_diana_falha' => ['is_checkbox_checked']
            ],
            'array' => [
                'evento' => ['is_not_empty', 'no_events'],
                'status' => ['is_not_empty'],
                'owner' => ['is_array', 'not_contains_negative_one'],
                'prioridade' => ['is_array', 'not_contains_negative_one'],
                'estabelecimento_modal' => ['is_array', 'not_contains_negative_one'],
                'ncm_proposta_modal' => ['is_array', 'not_contains_negative_one'],
                'sistema_origem_modal' => ['is_array', 'not_contains_negative_one'],
                'novo_status_atributos' => ['is_array', 'not_contains_negative_one'],
                'ex_ipi_modal' => ['is_array', 'not_contains_negative_one'],
                'ex_ii_modal' => ['is_array', 'not_contains_negative_one']
            ],
            'simple' => [
                'search' => ['is_not_empty']
            ]
        ];
    }

    /**
     * Obtém configuração para tela de relatórios
     *
     * @return array
     */
    public static function get_relatorio_config()
    {
        return [
            'boolean' => [
                'incluir_inativos' => ['is_checkbox_checked'],
                'exportar_detalhado' => ['is_checkbox_checked']
            ],
            'array' => [
                'empresas' => ['is_array', 'not_contains_negative_one'],
                'periodos' => ['is_array', 'not_contains_negative_one'],
                'tipos_relatorio' => ['is_array', 'not_contains_negative_one']
            ],
            'simple' => [
                'codigo_relatorio' => ['is_not_empty'],
                'nome_relatorio' => ['is_not_empty']
            ]
        ];
    }
}
