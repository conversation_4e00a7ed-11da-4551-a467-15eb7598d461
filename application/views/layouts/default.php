<!DOCTYPE html>
<script type="text/javascript" src="<?php echo base_url('assets/js/jquery.min.js'); ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/bootstrap.min.js'); ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/sweetalert.min.js'); ?>"></script>

<?php if (has_role('sysadmin')) : ?>
    <script type="text/javascript" src="<?php echo base_url('assets/js/controle_pendencias/admin.js?v=' . time()); ?>"></script>
<?php endif; ?>
<?php if (has_role('sysadmin') || !has_role('cliente_pmo')) : ?>
    <script type="text/javascript" src="<?php echo base_url('assets/js/controle_pendencias/user.js?v=' . time()); ?>"></script>
<?php endif; ?>
<?php

if (isset($javascript)) {
    echo $javascript;
}

?>

<script type="text/javascript" charset="utf-8">
    var $base_url = '<?php echo base_url() ?>';

    function toggle_checkbox(o) {
        var checkboxes = $("input[type=checkbox][data-toggle=true]");
        checkboxes.prop('checked', $(o).prop('checked')).trigger("change");
    }

    $(function() {
        $('*[data-toggle="tooltip"]').tooltip();
    });

    $(function() {
        $('.container-alerta-pendencias .top-bar').on('click', function() {
            if ($(this).parent().hasClass('opened')) {
                $(this).parent().removeClass('opened');

                $(this).find('.down-caret').addClass('glyphicon-chevron-up');
                $(this).find('.down-caret').removeClass('glyphicon-chevron-down');

            } else {
                $(this).parent().addClass('opened');

                $(this).find('.down-caret').addClass('glyphicon-chevron-down');
                $(this).find('.down-caret').removeClass('glyphicon-chevron-up');
            }
        });

        // $(window).scroll(function() {

        //     if ($(window).scrollTop() + $(window).height() >= $('.footer-text').offset().top) {
        //         bottom = $(window).scrollTop() + $(window).height() - $('footer').offset().top;
        //         $('.float-bottom-alert').css('bottom', bottom + 'px');
        //     } else {
        //         $('.float-bottom-alert').css('bottom', '0px');
        //     }

        // });
    });
</script>
<?php
$CI = &get_instance();

if (!isset($CI->version_model)) {
    $CI->load->model('version_model');
}
if (!isset($CI->empresa_model)) {
    $CI->load->model('empresa_model');
}
if (!isset($CI->usuario_model)) {
    $CI->load->model('usuario_model');
}

$company_logo_filename = NULL;
$company_info_nome_fantasia_curto = 'Empresa';  

if (sess_user_company()) {
    $where_company = ['ativo' => 1];
    $company_info_obj = $CI->empresa_model->get_entry(sess_user_company(), $where_company);
 
    if ($company_info_obj) {
        $company_logo_filename = $CI->empresa_model->get_logo_url($company_info_obj->logo_filename);
        $company_info_nome_fantasia_curto = strtok($company_info_obj->nome_fantasia, " ");
    }
}
if (empty($company_logo_filename)  ) {
    $company_logo_filename = null;
}


$versao_sistema = $CI->version_model->get_version();
$page_title = isset($title) ? $title : 'Gestão Tarifária';

$empresa_atual_obj = $CI->empresa_model->get_entry(sess_user_company());
$campos_adicionais = [];
$funcoes_adicionais = [];
if ($empresa_atual_obj && isset($empresa_atual_obj->campos_adicionais)) {
    $campos_adicionais = explode("|", $empresa_atual_obj->campos_adicionais);
}
if ($empresa_atual_obj && isset($empresa_atual_obj->funcoes_adicionais)) {
    $funcoes_adicionais = explode("|", $empresa_atual_obj->funcoes_adicionais);
}

$total_perguntas_pendentes_controller = isset($total_perguntas) ? $total_perguntas : 0;

?>
<html>

<head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8">
    <title><?php if (ENVIRONMENT != 'production') {
                echo 'Homologação - ';
            } ?>Gestão Tarifária <?php echo isset($title) ? '› ' . html_escape($title) : ''; ?></title>
    <?php
    echo link_tag('assets/css/bootstrap.min.css?v=1.2');
    echo link_tag('assets/css/bootstrap-glyphicons.css?v=1');
    echo link_tag('assets/css/font-awesome.min.css');
    echo link_tag('assets/css/new-layout.css?v=' . time());
    echo link_tag('assets/css/sweetalert.css');
    echo link_tag('assets/css/main.css?v=1');
    if (isset($stylesheets)) {
        echo $stylesheets;
    }
    ?>
    <link rel="shortcut icon" href="<?php echo base_url('assets/img/icon-becomex.png'); ?>" type="image/x-icon">
    <link rel="icon" href="<?php echo base_url('assets/img/icon-becomex.png'); ?>" type="image/x-icon">
    <link href="https://cdn.jsdelivr.net/npm/vue-loading-overlay@4/dist/vue-loading.css" rel="stylesheet">
</head>

<body>
    <script>
        $(document).ready(function() {
            $.ajax({
                url: '<?php echo base_url("home/ajax_get_pendencias") ?>',
                success: function(r) {
                    if (r == '' || r == null) return false;

                    // Assume r contains the count directly or as r.data
                    var count = (typeof r === 'object' && r.data !== undefined) ? parseInt(r.data) : parseInt(r);

                    if (!isNaN(count) && count > 0) {
                        $('#total_to_answer').text(count).show(); // Update text and show the badge
                    } else {
                        $('#total_to_answer').hide(); // Hide badge if count is 0 or invalid
                    }
                },
                error: function() {
                    console.error("Erro ao buscar pendências.");
                    $('#total_to_answer').hide(); // Hide badge on error
                }
            });
        })
    </script>
    <input type="hidden" name="base_url" id="base_url" value="<?php echo base_url(); ?>">
    <input type="hidden" id="site_url" value="<?php echo site_url(); ?>">
    <input type="hidden" id="lang" name="lang" value="<?php echo $this->session->userdata('site_lang'); ?>">
    <input type="hidden" id="sess_user_company_id" value="<?php echo sess_user_company(); ?>">
    <input type="hidden" id="sess_user_id_js" value="<?php echo sess_user_id(); ?>">
    <input type="hidden" id="current_page_uri" value="<?php echo $this->uri->uri_string(); ?>">
    <input type="hidden" id="csrf_token_name" value="<?php echo $this->security->get_csrf_token_name(); ?>">
    <input type="hidden" id="csrf_hash" value="<?php echo $this->security->get_csrf_hash(); ?>">

    <?php if (ENVIRONMENT != 'production') : ?>
        <style>
            .subnav {
                margin-top: 0px !important;
            }
        </style>
        <div class="alert alert-warning text-center" role="alert" style="margin-bottom: 0px;">
            <strong>Atenção!</strong> Você está acessando o ambiente de <strong>HOMOLOGAÇÃO</strong>.
        </div>
    <?php endif; ?>

    <div id="app-sidebar" style="z-index: 3000;background: url('/assets/icon/sidebar.svg');   
            background-size: cover;
        background-position: center;
        background-repeat: no-repeat; ">

        <!-- <a href="#" id="sidebar-scroll-up-button" class="sidebar-scroll-nav-arrow" title="Rolar para cima" style="display: none;">
            <i class="fa fa-chevron-up"></i>
        </a> -->

        <div class="sidebar-scrollable-icons-container">
            <ul class="sidebar-nav-icons">
                <!-- <li> <br></li> -->

                <li>
                    <a href="#" class="sidebar-icon-trigger" data-menu-key="menu_principal" data-toggle="modal" data-target="#mainNavigationModal">
                        <img style="width: 20px; height: 20px;" src="<?php echo base_url('assets/icon/menu.svg'); ?>" class="custom-svg-icon" alt="Gerenciar">
                        <div class="sidebar-tooltip">
                            <div class="sidebar-tooltip-content">
                                <div><strong>Gerenciar</strong></div>
                            </div>
                        </div>
                    </a>
                </li>
                <li>
                    <a href="<?php echo base_url('cockpit'); ?>" class="sidebar-icon-trigger-menu" data-menu-key="home">
                        <img style="width: 20px; height: 20px;" src="<?php echo base_url('assets/icon/home.svg'); ?>" class="custom-svg-icon" alt="Cockpit">
                        <div class="sidebar-tooltip">
                            <div class="sidebar-tooltip-content">
                                <div><strong>Cockpit</strong></div>
                            </div>
                        </div>
                    </a>
                </li>
 
                <li>
                    <a href="<?php echo base_url("/cadastros/mestre_itens"); ?>" class="sidebar-icon-trigger-menu" data-menu-key="mestre_itens">
                        <img style="width: 20px; height: 20px;" src="<?php echo base_url('assets/icon/menu_mestre_itens.svg'); ?>" class="custom-svg-icon" alt="Mestre de Itens">
                        <div class="sidebar-tooltip">
                            <div class="sidebar-tooltip-content">
                                <div><strong>Mestre de itens</strong></div>
                            </div>
                        </div>
                    </a>
                </li>
                <li>
                    <a href="<?php echo base_url("/atribuir_grupo"); ?>" class="sidebar-icon-trigger-menu" data-menu-key="dados_tecnicos">
                        <img style="width: 20px; height: 20px;" src="<?php echo base_url('assets/icon/menu_dados_tecnicos.svg'); ?>" class="custom-svg-icon" alt="Dados Técnicos">
                        <div class="sidebar-tooltip">
                            <div class="sidebar-tooltip-content">
                                <div><strong>Dados Técnicos</strong></div>
                            </div>
                        </div>
                    </a>
                </li>
                <li>
                    <a href="<?php echo base_url("homologacao"); ?>" class="sidebar-icon-trigger-menu" data-menu-key="homologacao">
                        <img style="width: 20px; height: 20px;" src="<?php echo base_url('assets/icon/menu_homologacao.svg'); ?>" class="custom-svg-icon" alt="Homologação">
                        <div class="sidebar-tooltip">
                            <div class="sidebar-tooltip-content">
                                <div><strong>Homologação</strong></div>
                            </div>
                        </div>
                    </a>
                </li>
                <li>
                    <a href="<?php echo base_url("/wf/atributos"); ?>" class="sidebar-icon-trigger-menu" data-menu-key="analise_atributos">
                        <img style="width: 20px; height: 20px;" src="<?php echo base_url('assets/icon/menu_analise_atributos.svg'); ?>" class="custom-svg-icon" alt="Análise de atributos">
                        <div class="sidebar-tooltip">
                            <div class="sidebar-tooltip-content">
                                <div><strong>Análise de atributos</strong></div>
                            </div>
                        </div>
                    </a>
                </li>
                <li>
                    <a type="button" class="sidebar-icon-trigger-menu" data-toggle="modal" data-target="#perguntasRespostas" data-menu-key="realizar_perguntas">
                        <img style="width: 20px; height: 20px;" src="<?php echo base_url('assets/icon/menu_realizar_perguntas.svg'); ?>" class="custom-svg-icon" alt="Realizar perguntas">
                        <div class="sidebar-tooltip">
                            <div class="sidebar-tooltip-content">
                                <div><strong>Realizar perguntas</strong></div>
                            </div>
                        </div>
                    </a>
                </li>
                <li>
                    <a class="sidebar-icon-trigger-menu" data-menu-key="perguntas_pendentes" href="<?php echo site_url('controle_pendencias') ?>" type="button">
                        <img style="width: 20px; height: 20px;" src="<?php echo base_url('assets/icon/menu_perguntas_pendentes.svg'); ?>" class="custom-svg-icon" alt="Visualizar perguntas pendentes">
                        <p class="pull-left badge progress-bar-danger" id="total_to_answer" style="
                        position: absolute; 
                        top: -5px; 
                        right: -5px; 
                        left: auto; 
                        bottom: auto; 
                
                        padding: 2px 5px; 
                        font-size: 10px; 
                        line-height: 1; 
                        border-radius: 8px; 
                        display: none;">
                            <span class="spinner-border" style="width: 11px;height:11px; display: none;" role="status" aria-hidden="true"></span>
                        </p>
                        <div class="sidebar-tooltip">
                            <div class="sidebar-tooltip-content">
                                <div><strong>Visualizar perguntas pendentes</strong></div>
                            </div>
                        </div>
                    </a>
                </li>
                <li>
                    <a href="<?php echo base_url("controle_pendencias/perguntas"); ?>" class="sidebar-icon-trigger-menu" data-menu-key="perguntas_realizadas">
                        <img style="width: 20px; height: 20px;" src="<?php echo base_url('assets/icon/menu_perguntas_realizadas.svg'); ?>" class="custom-svg-icon" alt="Ver minhas perguntas realizadas">
                        <div class="sidebar-tooltip">
                            <div class="sidebar-tooltip-content">
                                <div><strong>Ver minhas perguntas realizadas</strong></div>
                            </div>
                        </div>
                    </a>
                </li>
                <li>
                    <a href="<?php echo base_url('login/logout'); ?>" class="sidebar-icon-trigger-menu" data-menu-key="sair">
                        <img style="width: 20px; height: 20px;" src="<?php echo base_url('assets/icon/menu_sair.svg'); ?>" class="custom-svg-icon" alt="Sair">
                        <div class="sidebar-tooltip">
                            <div class="sidebar-tooltip-content">
                                <div><strong>Sair</strong></div>
                            </div>
                        </div>
                    </a>
                </li>
  
 
            </ul>
        </div>

        <!-- <a href="#" id="sidebar-scroll-down-button" class="sidebar-scroll-nav-arrow" title="Rolar para baixo" style="display: none;">
            <i class="fa fa-chevron-down"></i>
        </a> -->
        <div id="sidebar-logo-container-bottom" style="padding: 0 20px 10px 20px !important;">
             <img src="<?php echo base_url('assets/icon/logo_becomex.svg'); ?>" alt="Becomex Logo">
        </div>
    </div>

    <div id="app-main-content">
    <div class="subnav">
                <div class="logo-container container">
                    <div class="row">
                        
                        <div class="col-md-3">
                        <?php if (!empty($company_logo_filename)) { ?>
                            <div class="logo">
                                <img src="<?php echo $company_logo_filename ?>" style="max-width: 162px; max-height: 64px;">
                            </div>
                            <?php if (customer_has_role('visualizar_informacoes_servico', sess_user_id())) : ?>
                            <button type="button" class="icon-btn" data-toggle="modal" data-target="#meuModal">
                                <span class="glyphicon glyphicon-info-sign"></span>
                            </button>
                            <?php endif; ?>
        
                        <?php } else { ?>
                            <!-- Placeholder para quando não houver logo -->
                            <div class="logo">
                                <span style="color: #fff; font-style: italic;"></span>
                            </div>
                        <?php } ?>
                        </div>

                        <div class="col-md-9 user-info"   style="    color: #fff;
                        font-size: 16px;
 
                        text-align: right;
                        margin-top: 10px;
                        padding: 10px;"> <span style="    display: inline-block;
                        font-family: 'Roboto', sans-serif;
                        font-weight: 700;
                        font-size: 18px;
                        line-height: 30px;
                        letter-spacing: 0;
                        vertical-align: middle;
                        color: #FFFFFF;
                        overflow: hidden;  
                        white-space: nowrap;
                        text-overflow: ellipsis; "><?php echo $this->session->userdata('user_nome'); ?>   <a href="/configuracao/editar_perfil" style="color: #ffff;">
                            <i class="fa fa-external-link"></i>                            
                        </a></span></div>
                                            
                    </div>
                </div>
            </div>
          

        <main id="page-content-area">
            <div class="container">
                <?php if (isset($this->breadcrumbs)) {
                    echo $this->breadcrumbs->show();
                } ?>
                <?php echo isset($show_message) ? $show_message : ''; ?>
                <?php echo $yield; ?>
            </div>
        </main>

 

    </div>
            <!-- Modal -->
            <div id="meuModal" class="modal fade" tabindex="-1" role="dialog" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; overflow:auto; background-color:rgba(0,0,0,0.5);">
                <div class="modal-dialog" role="document" style="position:relative; margin:50px auto; width:600px; max-width:90%;">
                    <div class="modal-content" style="background-color:#fff; border-radius:6px; box-shadow:0 5px 15px rgba(0,0,0,.5); border:1px solid #ccc;">
                        
                        <!-- Cabeçalho -->
                        <div class="modal-header" style="padding:15px; border-bottom:1px solid #e5e5e5;">
                            <button type="button" class="close" data-dismiss="modal" style="float:right; font-size:24px; font-weight:bold; border:none; background:none; cursor:pointer;">&times;</button>
                            <h4 class="modal-title" style="margin:0; font-size:18px; font-weight:bold;">Acordos e Premissas</h4>
                        </div>
                        
                        <!-- Corpo -->
                        <div class="modal-body" style="padding:20px; font-size:14px; line-height:1.5; color:#333;">
                            <h4 style="margin-top:0; font-size:15px; font-weight:bold;">Serviços contratados</h4>
                            <p style="margin:0 0 15px 0;"><?php echo !empty($company_info_obj->servicos_contratados) ? $company_info_obj->servicos_contratados : 'Nenhum serviço contratado' ?></p>
                            
                            <h4 style="margin-top:10px; font-size:15px; font-weight:bold;">Acordos e premissas</h4>
                            <ol style="padding-left:18px; margin:10px 0;"><?php echo !empty($company_info_obj->acordos_premissas) ? $company_info_obj->acordos_premissas : 'Nenhum acordo'; ?></ol>
                        </div>
                        
                        <!-- Rodapé -->
                        <div class="modal-footer" style="padding:10px 15px; text-align:right; border-top:1px solid #e5e5e5;">
                            <button type="button" class="btn btn-default" data-dismiss="modal" style="padding:6px 12px; border:1px solid #ccc; border-radius:4px; background-color:#f9f9f9; cursor:pointer;">Fechar</button>
                        </div>
                    </div>
                </div>
            </div>
    <div class="modal fade" id="mainNavigationModal" tabindex="-1" role="dialog" aria-labelledby="mainNavigationModalLabel">
        <div class="modal-dialog modal-xl" role="document" style="max-width: 80%;     padding-top: 5%;">
            <div class="modal-content">
                <div class="modal-header-custom">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="
                        position: absolute !important;
                        top: 0px !important;
                        right: 0px !important;
                        transform: translate(40%, -40%) scale(1) !important;
                        background: #6c757d;
                        color: white !important;
          
                        width: 25px !important;
                        height: 25px !important;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 9999 !important;
                        cursor: pointer !important;
                        font-weight: 300 !important;
                        font-size: xx-large !important;
                        position-area: end !important;
                        border-radius: 7px !important;
                    ">
                        ×
                    </button>
                </div>
                <div class="modal-body-custom">
                    <div class="row no-gutters" style=" font-weight: 500;">

                        <div class="col-lg-4 nav-col nav-col-1" id="nav-col-1" style="border-radius: 8px 0 0 8px !important; margin-left: 15px; box-shadow: 5px 0 8px -2px rgba(0, 0, 0, 0.3); position: relative; z-index: 1;">
                            <div class="nav-col-search-container">
                                <!-- Incluir um padding no input para o texto nao ficar colado na borda -->
                                <input type="text" id="nav-modal-filter" class="nav-filter-input input-linha" placeholder="Pesquisar por..." style="padding-left: 10px !important;">
                            </div>
                            <ul class="nav-list-group" id="nav-list-col1"></ul>
                        </div>
                        <div class="col-lg-8 nav-col nav-col-2" id="nav-col-2" style="display:none;">
                            <h5 class="nav-col-title" id="nav-col-title2" style="display:none;"></h5>
                            <ul class="nav-list-group" id="nav-list-col2"></ul>
                        </div>

                        <div class="col-lg-4 nav-col nav-col-3" id="nav-col-3" style="display:none;">
                            <h5 class="nav-col-title" id="nav-col-title3" style="display:none;"></h5>
                            <ul class="nav-list-group" id="nav-list-col3"></ul>
                        </div>

                        <div class="col-lg-3 nav-col nav-col-4" id="nav-col-4" style="display:none;">
                            <h5 class="nav-col-title" id="nav-col-title4" style="display:none;"></h5>
                            <ul class="nav-list-group" id="nav-list-col4"></ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <style>
            .logo-info {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 10px;
            }

            .icon-btn {
            margin-left: 210px;
            margin-top: 30px;
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 22px;
            }

            .icon-btn .glyphicon {
            color: #ffffff !important;
            }
            .icon-btn:hover .glyphicon {
            color: #cccccc !important;
            }

        </style>

        <script>
            var modal = document.getElementById("meuModal");
            var btn = document.getElementById("btnModal");
            var span = document.getElementsByClassName("close")[0];

            if (modal.style.display)
            {
                if (btn) {
                    btn.onclick = function() { modal.style.display = "block"; };
                }
                if (span) {
                    span.onclick = function() { modal.style.display = "none"; }
                }
                
                window.onclick = function(e) {
                    if (e.target == modal) modal.style.display = "none";
                }
            }
        </script>

    <div class="modal fade" id="modal-email" style="z-index: 1060">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
                    <h4 class="modal-title">Desconheço o item</h4>
                </div>
                <div class="modal-body">
                    <form method="POST" action="<?php echo site_url('controle_pendencias/sugerir'); ?>" id="form_email_share">
                        <div class="form-group">
                            <label>Digite o e-mail do usuário sugerido</label>
                            <div class="input-group">
                                <select class="form-control" name="email_share">
                                    <option disabled selected>Selecione um usuário responsável</option>
                                    <?php
                                    if (isset($CI->usuario_model) && sess_user_company()) {
                                        $CI->usuario_model->clear_states();
                                        $CI->usuario_model->set_state('filter.id_empresa', sess_user_company());
                                        $users_modal_email = $CI->usuario_model->get_entries();
                                        if ($users_modal_email) {
                                            foreach ($users_modal_email as $user_me) {
                                                echo "<option value='" . html_escape($user_me->email) . "'>" . html_escape($user_me->email) . "</option>";
                                            }
                                        }
                                    }
                                    ?>
                                </select>
                                <span class="input-group-addon"><i class="glyphicon glyphicon-user"></i></span>
                            </div>
                        </div>
                        <div class="form-group" style="margin-top: 15px;">
                            <textarea rows="2" name="comment" class="form-control" placeholder="Adicione um comentário..."></textarea>
                        </div>
                        <input type="hidden" name="<?php echo $this->security->get_csrf_token_name(); ?>" value="<?php echo $this->security->get_csrf_hash(); ?>">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Fechar</button>
                        <button id="save_email_share" type="submit" class="btn btn-primary">Enviar</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="perguntasPendentes" tabindex="-1" role="dialog" aria-labelledby="perguntasPendentesLabel" style="z-index: 1060">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
                    <h3 class="modal-title" id="perguntasPendentesLabel">Pendência de Respostas</h3>
                </div>
                <div class="modal-body min-height-pendencias-modal">
                    <div class="col-md-12">
                        Olá <strong><?php echo sess_user_nome(); ?></strong>, você tem algumas perguntas pendentes de respostas.
                        <div style="margin-top:15px;">
                            <a href="<?php echo base_url("controle_pendencias"); ?>" class="btn btn-primary">Clique aqui para acessar</a>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-default" data-dismiss="modal" aria-label="Close">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <?php
    $has_owner_var = isset($has_owner) ? $has_owner : false;
    $this->load->view('atribuir_grupo/modal-perguntas-respostas', ['has_owner' => $has_owner_var]);
    ?>



    <?php if (isset($javascript)) {
        echo $javascript;
    } ?>

    <script type="text/javascript">
        const generatedMenuConfig = {
            'home': {
                title: 'Início',
                icon: 'glyphicon-home',
                directUrl: '<?php echo base_url(); ?>',
                is_visible: true
            },
            'consulta_diana': {
                title: 'Consulta Diana',
                icon_img: '<?php echo base_url("assets/img/diana/diana_icon_white.png"); ?>',
                icon_fa: 'fa-bullseye',
                directUrl: '<?php echo base_url("consulta_diana"); ?>',
                is_visible: <?php echo has_role("consulta_diana") ? 'true' : 'false'; ?>
            },
            'cockpit': {
                title: 'Cockpit',
                icon: 'glyphicon-stats',
                directUrl: '<?php echo base_url('cockpit'); ?>',
                is_visible: true
            },

            'mestre_itens': {
                title: 'Mestre de Itens',
                icon_img: "<?php echo base_url('assets/icon/clipboard.svg'); ?>",
                is_visible: <?php echo has_role('gerenciar_mestre') ? 'true' : 'false'; ?>,
                url: "<?php echo site_url('cadastros/mestre_itens'); ?>",
                showCol1NewTabIcon: true,
                url_col1_new_tab: "<?php echo site_url('cadastros/mestre_itens'); ?>",
                // open_tab_on_item_click: false,
            },


            'dados_tecnicos': {
                title: 'Dados Técnicos',
                col2IsSections: true,
                icon_img: "<?php echo base_url('assets/icon/list-check.svg'); ?>",
                is_visible: <?php echo customer_has_role('dados_tecnicos', sess_user_id()) ? 'true' : 'false'; ?>,

                forceEmptyCol3: true,
                col2: [
                    <?php if (customer_has_role('dados_tecnicos_geral', sess_user_id())) : ?> {
                            label: 'Por Empresa',
                            url: '<?php echo site_url('atribuir_grupo'); ?>',
                            open_tab: true,
                        },
                        {
                            label: 'Geral (SLA)',
                            url: '<?php echo site_url('geral_sla') . '?reset_filters=1'; ?>',
                            open_tab: true,
                        },
                    <?php elseif (customer_has_role('dados_tecnicos', sess_user_id())) : ?> {
                            label: 'Dados Técnicos',
                            url: '<?php echo site_url('atribuir_grupo'); ?>'
                        },
                    <?php endif; ?>
                ].filter(Boolean)
            },
            'homologacao': {
                title: 'Homologação',
                col2IsSections: true,
                icon_img: "<?php echo base_url('assets/icon/check.svg'); ?>",
                is_visible: <?php echo (customer_has_role('preencher_atributos_workflow', sess_user_id()) || customer_has_role('homologar_atributos_workflow', sess_user_id()) || customer_has_role('movimentar_itens_workflow', sess_user_id()) || customer_has_role('homologacao', sess_user_id())) ? 'true' : 'false'; ?>,
                forceEmptyCol3: true,
                col2: [
                    <?php if ((customer_has_role('preencher_atributos_workflow', sess_user_id()) || customer_has_role('homologar_atributos_workflow', sess_user_id()) || customer_has_role('movimentar_itens_workflow', sess_user_id())) && customer_has_role('homologacao', sess_user_id())) : ?> {
                            label: 'Homologação',
                            url: '<?php echo site_url("homologacao"); ?>',
                            open_tab: true,
                        },
                        {
                            label: 'Análise de Atributos',
                            url: '<?php echo site_url("wf/atributos"); ?>',
                            open_tab: true,
                        },
                    <?php elseif (customer_has_role('homologacao', sess_user_id())) : ?> {
                            label: 'Homologação',
                            url: '<?php echo site_url("homologacao"); ?>',
                            open_tab: true,
                        }
                    <?php elseif (customer_has_role('preencher_atributos_workflow', sess_user_id()) || customer_has_role('homologar_atributos_workflow', sess_user_id()) || customer_has_role('movimentar_itens_workflow', sess_user_id())) : ?> {
                        label: 'Análise de Atributos',
                        url: '<?php echo site_url("wf/atributos"); ?>',
                        open_tab: true,
                    }
                    <?php endif; ?>
                ].filter(Boolean)
            },

            'catalogo_produtos': {
                title: 'Catálogo de produtos',
                col2IsSections: true,
                is_visible: true,
                icon_img: "<?php echo base_url('assets/icon/book.svg'); ?>",

                sections: {
                    'produtos': {
                        title: 'Produtos',
                        col2IsSections: true,
                        open_tab: true,
                        is_visible: <?php echo (has_role('gerenciar_empresas') || has_role('gerenciar_usuarios') || has_role('consultor') || has_role('gerenciar_perfil') || customer_has_role('gerenciar_paises', sess_user_id()) || in_array('unidade_negocio', $campos_adicionais) || in_array('owner', $campos_adicionais) || customer_has_role('gerenciar_prioridades', sess_user_id())) ? 'true' : 'false'; ?>,
                        col3: [
                            <?php if (has_role('gerenciar_empresas')) {
                                echo "{ new_tab: true, label: 'Importar do Produto GT', url: 'https://app.gestaotarifaria.com.br/importar-gt-multitenant' },";
                            } ?>
                            <?php if (has_role('gerenciar_usuarios') || has_role("consultor")) {
                                echo "{ new_tab: true, label: 'Consulta de produtos', url: 'https://app.gestaotarifaria.com.br/produtos-multitenant' },";
                            } ?>
                            <?php if (in_array('exportar_diana', $funcoes_adicionais)) {
                                echo "{ new_tab: true, label: 'Upload de atributos', url: 'https://app.gestaotarifaria.com.br/upload-atributos-cliente-gt' },";
                            } ?>

                        ].filter(Boolean)
                    },
                    'operador_estrangeiro': {
                        title: 'Operador estrangeiro',
                        is_visible: <?php echo (customer_can('ii') && (has_role('monitor_ex') || has_role('monitor_ex_apuracao'))) ? 'true' : 'false'; ?>,
                        col3: [
                            <?php if (customer_can('ii') && (has_role('monitor_ex'))) {
                                echo "{ new_tab: true, label: 'Cadastro e Listagem', url: 'https://app.gestaotarifaria.com.br/operador-estrangeiro' },";
                            } ?>
                            <?php if (customer_can('ii') && (has_role('monitor_ex_apuracao'))) {
                                echo "{ new_tab: true, label: 'Vincular Produtos a Operador Estrangeiro', url: 'https://app.gestaotarifaria.com.br/operador-produto' },";
                            } ?>
                            <?php if (customer_can('ii') && (has_role('monitor_ex_apuracao'))) {
                                echo "{ new_tab: true, label: 'Importação de Vinculos de Produtos a Operador Estrangeiro', url: 'https://app.gestaotarifaria.com.br/importar-vinculos-produtos-operador-estrangeiro' },";
                            } ?>
                        ].filter(Boolean)
                    },
                    'relatorio_pucomex': {
                        title: 'Relatório de itens integrados ao PUCOMEX/Cliente',
                        is_visible: <?php echo (has_role('gerenciar_capitulos') || has_role('gerenciar_secao') || (has_role('gerenciar_cest') && customer_can('cest')) || (has_role('gerenciar_atribuicao') && customer_can('cest'))) ? 'true' : 'false'; ?>,
                        url: "https://app.gestaotarifaria.com.br/itens-integrados-pucomex",
                        showCol1NewTabIcon: true,
                        new_tab: true,
                        url_col1: "https://app.gestaotarifaria.com.br/itens-integrados-pucomex",
                    }
                }
            },
            'arquivos': {
                title: 'Arquivos',
                icon_img: "<?php echo base_url('assets/icon/arquivos.svg'); ?>",
                col2IsSections: true,
                is_visible: true,
                forceEmptyCol3: true,
                col2: [
                    <?php if (has_role('arquivos_mestre')) {
                        echo "{ open_tab: true, label: 'Enviar Mestre de Itens', url: '" . site_url("upload") . "'  },";
                    } ?>
                    <?php if (has_role('arquivos_homologacao')) {
                        echo "{ open_tab: true, label: 'Enviar Itens para Homologação', url: '" . site_url("uploadnext") . "' },";
                    } ?>
                    <?php if (has_role('booking_eletronico')) {
                        echo "{ open_tab: true, label: 'Enviar Itens para Booking Eletrônico', url: '" . site_url("uploadbooking") . "' },";
                    } ?>
                    <?php if (has_role('arquivos_implementacao')) {
                        echo "{ open_tab: true, label: 'Enviar Itens para Implementação', url: '" . site_url("implementacao/upload") . "' },";
                    } ?>
                    <?php if (has_role('arquivos_sugestao')) {
                        echo "{ open_tab: true, label: 'Enviar Itens para Sugestão de Grupo Tarifário', url: '" . site_url("uploaddescricao") . "' },";
                    } ?>
                ].filter(Boolean)
            },
            'gerenciar': {
                title: 'Gerenciar',
                icon_img: "<?php echo base_url('assets/icon/gerenciar.svg'); ?>",
                col2IsSections: true,
                is_visible: <?php echo (has_role('gerenciar_atribuicao') || has_role('gerenciar_auditoria_monitor') || has_role('gerenciar_auditoria_fator') ||
                        has_role('gerenciar_busca') || has_role('gerenciar_capitulos') || has_role('gerenciar_empresas') || has_role('gerenciar_cest') ||
                        has_role('gerenciar_fotos') || has_role('gerenciar_grupos_cadastro') || has_role('gerenciar_grupos_logs') ||
                        has_role('visualizar_hierarquia') || has_role('gerenciar_hierarquia') || has_role('gerenciar_logs') ||
                        has_role('gerenciar_mestre') || has_role('gerenciar_perfil') || has_role('gerenciar_score') || has_role('gerenciar_secao') ||
                        has_role('gerenciar_segmentos') || has_role('gerenciar_usuarios')) ? 'true' : 'false'; ?>,
                sections: {
                    'administracao': {
                        title: 'Administração',
                        
                        is_visible: <?php echo (has_role('gerenciar_atribuicao') || has_role('gerenciar_auditoria_monitor') || has_role('gerenciar_auditoria_fator') ||
                        has_role('gerenciar_busca') || has_role('gerenciar_capitulos') || has_role('gerenciar_empresas') || has_role('gerenciar_cest') ||
                        has_role('gerenciar_fotos') || has_role('gerenciar_grupos_cadastro') || has_role('gerenciar_grupos_logs') ||
                        has_role('visualizar_hierarquia') || has_role('gerenciar_hierarquia') || has_role('gerenciar_logs') ||
                        has_role('gerenciar_mestre') || has_role('gerenciar_perfil') || has_role('gerenciar_score') || has_role('gerenciar_secao') ||
                        has_role('gerenciar_segmentos') || has_role('gerenciar_usuarios')) ? 'true' : 'false'; ?>,
                        col3: [
                            <?php if (in_array('visualizar_bi', $funcoes_adicionais) && customer_has_role('visualizar_bi', sess_user_id())) {
                                echo "{ key: 'sla_admin', label: 'Indicadores de Sucesso (SLA)', url: 'https://beconnect.becomex.com.br/gestao/indicador/gestao-tarifaria/indicador-sucesso-sla',  open_tab: true },";
                            } ?>
                            <?php if (customer_has_role('gerenciar_paises', sess_user_id())) {
                                echo "{ key: 'paises_adm', label: 'Países', url: '" . site_url("/cadastros/pais") . "',  open_tab: true },";
                            } ?>
                            <?php if (has_role('gerenciar_empresas')) {
                                echo "{ key: 'empresas_adm', label: 'Empresas', url: '" . site_url("cadastros/empresa") . "',  open_tab: true },";
                            } ?>
                            <?php if (has_role('gerenciar_perfil')) {
                                echo "{ key: 'perfil_usuario_adm', label: 'Perfil de usuário', url: '" . site_url("cadastros/perfil") . "',  open_tab: true },";
                            } ?>
                            <?php if (has_role('gerenciar_usuarios') || has_role("consultor")) {
                                echo "{ key: 'usuarios_adm', label: 'Usuários', url: '" . site_url("cadastros/usuario") . "',  open_tab: true },";
                            } ?>
                            <?php if (true) {
                                echo "{ key: 'copias_gt_qa_adm', label: 'Cópias da base do GT para QA', url: 'https://app.gestaotarifaria.com.br/copia-producao-validacao-qa-gt',  new_tab: true },";
                            } ?>
                            <?php if (true) {
                                echo "{ key: 'copia_portal_gt_adm', label: 'Cópia de Produtos Portal GT', url: 'https://app.gestaotarifaria.com.br/copia-produtos-gt',  new_tab: true },";
                            } ?>
                            <?php if (in_array('unidade_negocio', $campos_adicionais)) {
                                echo "{key: 'monitor_flat_file_adm', label: 'Monitor Flat File', url: 'https://app.gestaotarifaria.com.br/monitor-flat-file ', new_tab: true},";
                            } ?>
                            <?php if (in_array('owner', $campos_adicionais)) {
                                echo "{key: 'owner_adm', label: 'Owner', url: 'https://app.gestaotarifaria.com.br/owner', new_tab: true},";
                            } ?>
                            <?php if (true) {
                                echo "{ key: 'rel_mov_itens_adm', label: 'Relatório de movimentação de itens', url: 'https://app.gestaotarifaria.com.br/movimentacao-itens-gt',  new_tab: true },";
                            } ?>
                            <?php if (in_array('unidade_negocio', $campos_adicionais)) {
                                echo "{key: 'unidade_negocio_adm', label: 'Unidade de negócio', url: 'https://app.gestaotarifaria.com.br/unidade', new_tab: true},";
                            } ?>
                            <?php if (has_role('gerenciar_segmentos')) {
                                echo "{ key: 'segmentos_adm', label: 'Segmentos', url: '" . site_url("cadastros/segmento") . "',  open_tab: true },";
                            } ?>
                            <?php if (customer_has_role('gerenciar_prioridades', sess_user_id())) : echo "{key: 'prioridades_adm', label: 'Prioridades', url: '" . site_url("cadastros/prioridades") . "',  open_tab: true},";
                            endif; ?>
                        ].filter(Boolean)
                    },
                    'beneficios_fiscais': {
                        title: 'Benefícios fiscais',
                        
                        is_visible: <?php echo (customer_can('ii') && (has_role('monitor_ex') || has_role('monitor_ex_apuracao'))) ? 'true' : 'false'; ?>,
                        col3: [
                            <?php if (customer_can('ii') && (has_role('monitor_ex'))) {
                                echo "{ key: 'monitor_ex_fta_bf', label: 'Monitor EX/FTA', url: '" . site_url("monitor_ex") . "',  open_tab: true },";
                            } ?>
                            <?php if (customer_can('ii') && (has_role('monitor_ex_apuracao'))) {
                                echo "{ key: 'monitor_ex_apuracao_bf', label: 'Monitor EX/FTA - Apuração de Ganhos', url: '" . site_url("monitor_ex_apuracao_ganhos") . "',  open_tab: true },";
                            } ?>
                        ].filter(Boolean)
                    },
                    'classificacao_fiscal': {
                        title: 'Classificação fiscal',
                        
                        is_visible: <?php echo (has_role('consultor') || has_role('sysadmin') || has_role('gerenciar_capitulos') ||   has_role('gerenciar_hierarquia')) ? 'true' : 'false'; ?>,
                        col3: [
                            <?php if (has_role('sysadmin') || has_role('consultor')) : echo "{key: 'grupo_perguntas_cf', label: 'Grupo de Perguntas', url: '" . site_url("grupo_perguntas") . "',  open_tab: true},";
                            endif; ?>
                            <?php if (has_role('gerenciar_grupos_cadastro') || has_role('gerenciar_grupos_logs') || has_role('visualizar_hierarquia') || has_role('gerenciar_hierarquia')) {
                                echo "{ 
                        key: 'grupos_tarifarios_cf', 
                        label: 'Grupos Tarifários', 
                         
                        url: '#', 
                        col4: [
                            " . (has_role('gerenciar_grupos_cadastro') ? "{ key: 'gt_cadastro', label: 'Cadastro', url: '" . site_url("cadastros/grupotarifario") . "',  open_tab: true }," : "") . "
                            " . (has_role('gerenciar_grupos_logs') ? "{ key: 'gt_historico', label: 'Histórico Grupo tarifários', url: '" . site_url("cadastros/logs_grupos") . "',  open_tab: true }," : "") . "
                            " . ((has_role('visualizar_hierarquia') || has_role('gerenciar_hierarquia')) ? "{ key: 'gt_hierarquia', label: 'Hierarquia', url: '" . site_url("hierarquia") . "',  open_tab: true }," : "") . "
                        ].filter(Boolean) 
                    },";
                            } ?>
                            <?php if (has_role('gerenciar_grupos_logs')) {
                                echo "{ key: 'hist_item_cf', label: 'Histórico do Item', url: '" . site_url("/cadastros/logs") . "',  open_tab: true },";
                            } ?>
                            <?php if (config_item('diplay_menu_vinculacao') == 1 || config_item('diplay_menu_vinculacao') == 2) : echo "{key: 'pend_atribuicoes_cf', label: 'Pendência de Atribuições', url: '" . site_url("vinculacao") . "',  open_tab: true},";
                            endif; ?>
                                <?php if (has_role('consulta_diana')) {
                                echo "{ key: 'diana', label: '  DIANA', url: '" . site_url("/consulta_diana") . "',  open_tab: true },";
                            } ?>
                        ].filter(Boolean)
                    },
                    'ncm_section': {
                        title: 'NCM',
                        
                        is_visible: <?php echo (has_role('gerenciar_capitulos') || has_role('gerenciar_secao') || has_role('gerenciar_cest') || has_role('gerenciar_atribuicao')) ? 'true' : 'false'; ?>,
                        col3: [
                            <?php if (has_role('gerenciar_capitulos')) {
                                echo "{ key: 'capitulo_ncm', label: 'Capítulo NCM', url: '" . site_url("cadastros/capitulos") . "',  open_tab: true },";
                            } ?>
                            <?php if (true) {
                                 echo "{ key: 'comp_ncm_orig_prop', label: 'Comparar NCM Original x Proposto', url: '" . site_url("/cenario") . "',  open_tab: true },";
                            } ?>
                            <?php if (has_role('ncm_comparar')) {
                                 echo "{ key: 'comp_ncms', label: 'Comparar NCMs', url: '" . site_url("/comparar_ncm") . "',  open_tab: true },";
                            } ?>
                            <?php if (has_role('ncm_consulta_itens_descricao')) {
                                 echo "{ key: 'cons_itens_desc_ncm', label: 'Consulta de Itens por Descrição', url: '" . site_url("/consulta/itens-descricao") . "',  open_tab: true },";
                            } ?>
                            <?php if (has_role('ncm_consulta_estatisticas')) {
                                 echo "{ key: 'cons_estat_ncm', label: 'Consulta Estatística', url: '" . site_url("/base_estatisticas") . "',  open_tab: true },";
                            } ?>
                            <?php if (has_role('ncm_consulta_itens_grupo')) {
                                 echo "{ key: 'cons_grupo_ncm', label: 'Consulta por Grupo', url: '" . site_url("/consulta/itens-grupo-tarifario") . "',  open_tab: true },";
                            } ?>
                            <?php if (has_role('ncm_explorar')) {
                                 echo "{ key: 'explorar_ncms', label: 'Explorar NCMs', url: '" . site_url("/explorar_ncm") . "',  open_tab: true },";
                            } ?>
                            <?php if (has_role('ncm_pre_selecao')) {
                                 echo "{ key: 'pre_sel_ex_ncm', label: 'Pré-Seleção EX', url: '" . site_url("/vinculacao/pre_selecao") . "',  open_tab: true },";
                            } ?>
                            <?php if ((has_role('ncm_consulta_ex_tarif'))) {
                                 echo "{ key: 'cons_ex_nve_ncm', label: 'Consulta Ex-tarifária/NVE e demais atributos', url: '" . site_url("/consulta_ex_tarifario") . "',  open_tab: true },";
                            } ?>
                            <?php if (has_role('gerenciar_secao')) {
                                echo "{ key: 'secao_capitulo_ncm', label: 'Seção do Capítulo', url: '" . site_url("cadastros/capitulos/grupos") . "',  open_tab: true },";
                            } ?>
                            <?php if (has_role('gerenciar_cest') && customer_can('cest') || has_role('gerenciar_atribuicao') && customer_can('cest')) {
                                echo "{ 
                        key: 'cest_ncm', 
                        label: 'CEST', 
                         
                        url: '#', 
                        col4: [
                            " . (true ? "{ key: 'atributos_cest', label: 'Atributos CEST', url: '" . site_url("/cadastros/cest") . "',  open_tab: true }," : "") . "
                            " . ((has_role('gerenciar_cest') && customer_can('cest')) ? "{key: 'explorar_cest', label: 'Explorar CEST', url: '" . site_url('cest/explorar') . "',  open_tab: true}," : "") . "
                            " . ((has_role('gerenciar_atribuicao') && customer_can('cest')) ? "{key: 'atribuicao_cest', label: 'Atribuição CEST', url: '" . site_url("cest") . "',  open_tab: true}," : "") . "
                        ].filter(Boolean) 
                    },";
                            } ?>
                        ].filter(Boolean)
                    },
                    'outros': {
                        title: 'Outros',
                        
                        is_visible: <?php echo (has_role('gerenciar_auditoria_monitor') ||   in_array('exportar_diana', $funcoes_adicionais)) ? 'true' : 'false'; ?>,
                        col3: [
                            <?php if (has_role('gerenciar_auditoria_monitor') || has_role('gerenciar_auditoria_fator')) {
                                echo "{ 
                        key: 'auditoria_nfe_outros', 
                        label: 'Auditoria de NFe', 
                         
                        url: '#', 
                        col4: [
                            " . (has_role('gerenciar_auditoria_monitor') ? "{key: 'monitor_nfe', label: 'Monitor de NFe', url: '" . site_url("monitor") . "',  open_tab: true}," : "") . "
                            " . (has_role('gerenciar_auditoria_fator') ? "{key: 'fator_conversao_nfe', label: 'Fator de conversão', url: '" . site_url("cadastros/fator_conversao") . "',  open_tab: true}," : "") . "
                        ].filter(Boolean) 
                    },";
                            } ?>
                            <?php if (has_role('gerenciar_busca')) {
                                echo "{ key: 'busca_fotos_outros', label: 'Busca de Fotos', url: '" . site_url("busca") . "',  open_tab: true },";
                            } ?>
                            <?php if (has_role('gerenciar_fotos')) {
                                echo "{ key: 'fotos_problemas_outros', label: 'Fotos com problemas', url: '" . site_url("issues") . "',  open_tab: true },";
                            } ?>
                            <?php if (has_role('gerenciar_score')) {
                                echo "{ key: 'score_aprovacao_outros', label: 'Score de Aprovação', url: '" . site_url("relatorios/score") . "',  open_tab: true },";
                            } ?>
                            <?php if (has_role('booking_eletronico')) {
                                echo "{ key: 'booking_eletronico_outros', label: 'Booking Eletrônico', url: '" . site_url("booking") . "',  open_tab: true },";
                            } ?>
                            <?php if (company_can("lessin") && (has_role("sysadmin") || has_role("consultor"))) : echo "{key: 'lessin_outros', label: 'Lessin', url: '" . site_url("lessin") . "',  open_tab: true},";
                            endif; ?>

                        ].filter(Boolean)
                    }
                }
            },
            'empresa_selecao': {
                title: 'Empresa - <?php echo "<span style=\"color:#4A934A\">" . $company_info_nome_fantasia_curto . "</span>"; ?>',
                col2IsSections: true,
                icon_img: "<?php echo base_url('assets/icon/empresa_v2.svg'); ?>",
                is_visible: <?php echo (has_role('sysadmin') || has_role('becomex_pmo') || has_role('engenheiro') || has_role('fiscal')) ? 'true' : 'false'; ?>, // Só mostra se o usuário pode trocar de empresa
                col2: [{
                    type: 'company_search'
                }]
            }
        };

        const empresasDataFromPHP = <?php
                                    $empresas_lista_js = [];
                                    if (isset($CI->empresa_model)) {
                                        $CI->empresa_model->clear_states();
                                        $CI->empresa_model->set_state('filter.order_by', 'nome_fantasia');
                                        $CI->empresa_model->set_state('ativo', '1');
                                        $empresas_php_list_modal = $CI->empresa_model->get_entries();
                                        if ($empresas_php_list_modal) {
                                            foreach ($empresas_php_list_modal as $emp_js) {
                                                $empresas_lista_js[] = [
                                                    'id' => $emp_js->id_empresa,
                                                    'nome' => $emp_js->nome_fantasia
                                                ];
                                            }
                                        }
                                    }
                                    echo json_encode($empresas_lista_js);
                                    ?>;
    </script>
    <script>
        const iconPath = "<?php echo base_url('assets/icon/seta_direita.svg'); ?>";
    </script>
    <script type="text/javascript" src="<?php echo base_url('assets/js/new-layout.js?v=' . time()); ?>"></script>

    <script>
        (function(i, s, o, g, r, a, m) {
            i['GoogleAnalyticsObject'] = r;
            i[r] = i[r] || function() {
                (i[r].q = i[r].q || []).push(arguments)
            }, i[r].l = 1 * new Date();
            a = s.createElement(o), m = s.getElementsByTagName(o)[0];
            a.async = 1;
            a.src = g;
            m.parentNode.insertBefore(a, m)
        })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');
        ga('create', 'UA-86212554-3', 'auto');
        ga('send', 'pageview');
    </script>
    <script type="text/javascript">
        (function(h, o, t, j, a, r) {
            h.hj = h.hj || function() {
                (h.hj.q = h.hj.q || []).push(arguments)
            };
            h._hjSettings = {
                hjid: 1179243,
                hjsv: 6
            };
            a = o.getElementsByTagName('head')[0];
            r = o.createElement('script');
            r.async = 1;
            r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
            a.appendChild(r);
        })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
    </script>

    <script type="text/javascript">
        $(function() {
            <?php if ((config_item('diplay_menu_vinculacao') == 1 || config_item('diplay_menu_vinculacao') == 3) && $this->uri->uri_string() !== 'vinculacao') { ?>
                setTimeout(function() {
                    let alerta_pendencias_vinculacao_div = $('.float-bottom-alert.container-alerta-pendencias');
                    if (alerta_pendencias_vinculacao_div && alerta_pendencias_vinculacao_div.length) {
                        let ajax_pendencias_vinculacao_xhr = $.ajax({
                            url: '<?php echo base_url("home/ajax_get_vinculacao"); ?>',
                            success: function(r) {
                                if (r == '') return false;
                                try {
                                    var data = $.parseJSON(r);
                                    if (data.total_geral_alertas > 0) {
                                        $(alerta_pendencias_vinculacao_div).find('.total').html(data.total_geral_alertas);
                                        $(alerta_pendencias_vinculacao_div).find('.ex_ii').html(data.ex_ii?.pendencias || 0);
                                        $(alerta_pendencias_vinculacao_div).find('.ex_ipi').html(data.ex_ipi?.pendencias || 0);
                                        $(alerta_pendencias_vinculacao_div).find('.nve').html(data.nve?.pendencias || 0);
                                        $(alerta_pendencias_vinculacao_div).find('.cest').html(data.cest?.pendencias || 0);
                                        $(alerta_pendencias_vinculacao_div).find('.atr').html(data.attrs?.pendencias || 0);
                                        $(alerta_pendencias_vinculacao_div).fadeIn();
                                    }
                                } catch (e) {
                                    console.error("Erro ao parsear JSON de vinculação:", e, r);
                                }
                            },
                            error: function(jqXHR, textStatus, errorThrown) {
                                console.error("Erro na requisição AJAX de vinculação:", textStatus, errorThrown);
                            }
                        });
                        $(window).on('beforeunload', function() {
                            if (ajax_pendencias_vinculacao_xhr && ajax_pendencias_vinculacao_xhr.readyState !== 4) {
                                ajax_pendencias_vinculacao_xhr.abort();
                            }
                        });
                    }
                }, 12000);
            <?php } ?>

            <?php if ($this->session->flashdata('show_perguntas_pendentes_modal') && $total_perguntas_pendentes_controller > 0) : ?>
                $("#perguntasPendentes").modal("show");
            <?php endif; ?>
        });
    </script>
</body>

</html>



<script>
//             $('li[data-item-key="empresa_selecao"]').on('click', function() {
 
//  $('#modal-company-filter-input').focus();
// });

document.addEventListener('DOMContentLoaded', function() {
  let botaoMenu = document.querySelector('[data-menu-key="menu_principal"]');

  if (botaoMenu) {
 
    botaoMenu.addEventListener('click', function() {

        
 
setTimeout(function() {


// Adicionado para tornar toda a área do li.nav-link-item clicável para sua ação principal,
    // exceto quando ícones de ação específicos são clicados.
 
    let isOpening = false;

    // Primeiro: remove qualquer handler anterior
    $("#mainNavigationModal").off("click", "li.nav-link-item");

    // Depois: registra o handler seguro
    $("#mainNavigationModal").on("click", "li.nav-link-item", function (e) {
        const $clickedListItem = $(this);
        const target = $(e.target);

        if (isOpening) return;

        // Evita clique nos ícones da direita
        if (target.closest(".item-actions-right").length > 0) return;

        const url = $clickedListItem.data("url");
        const newTab = $clickedListItem.data("new-tab");

        if (url && url !== "#") {
            isOpening = true;

            if (newTab === true || newTab === 'true') {
                window.open(url, "_blank");
            } else {
                window.location.href = url;
            }

            $("#mainNavigationModal").modal("hide");

            // Libera a trava após 1 segundo
            setTimeout(() => {
                isOpening = false;
            }, 1000);
        }
    });
 





        let mestreItem = document.querySelector('[data-item-key="mestre_itens"]');
     
        if (mestreItem) {
            let elemento = mestreItem.querySelectorAll('span');
            console.log(elemento);
            elemento.forEach(sp => {
            sp.style.setProperty('margin-right', '-5px', 'important');
            });
            let link = mestreItem.querySelector('a');
            let icons = mestreItem.querySelectorAll('img');
    
            mestreItem.addEventListener('click', function (event) {
                if (![...icons].includes(event.target)) {
                    link.click();
                }
            });
        }
      
    

       
    }, 300); // 2000 milissegundos = 2 segundos









    let botaoPergunta = document.querySelector('[data-menu-key="realizar_perguntas"]');
 
    if (botaoPergunta) {

        botaoPergunta.addEventListener('click', function() {

                var minhaModal = document.getElementById('mainNavigationModal');

                if (window.jQuery) {
                $('#mainNavigationModal').modal('hide');
                } else {

                var bootstrapModal = bootstrap.Modal.getInstance(minhaModal);
                if (!bootstrapModal) {
                    bootstrapModal = new bootstrap.Modal(minhaModal);
                }
                bootstrapModal.hide();
                }

        });
    }





    });
  }
});

</script>

<script>
    $(document).ready(function() {
        $('#mainNavigationModal').on('click', 'li.nav-list-item[data-item-key="empresa_selecao"]', function() {
            setTimeout(function() {
                $('#modal-company-filter-input').focus();
            }, 150);
        });
    });

</script>

