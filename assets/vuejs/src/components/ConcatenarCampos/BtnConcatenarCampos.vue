<template>
    <div>
        <button class="btn btn-primary" @click="handleClick" id="concatenar_valores">
            Concatenar campos
        </button>
        <input type="hidden" id="concatenar_campos" value="0">
    </div>
</template>
<script>
export default {
    data() {
        return {
            campos: [],
            hasLi: false,
            addedLi: false,
            hasNve: false,
            addedNve: false,
            hasSuframa: false,
            descricao: '',
            descResumida: '',
            descCompleta: '',
            subsidio: '',
            funcao: '',
            aplicacao: '',
            materialConstitutivo: '',
            marca: '',
            descCompText: '',
            suframaDescricao: ''
        }
    },
    props: {    
        id_empresa: {
            required: false,
            type: String | Number
        },
        preencher_descricao_resumida: {
            required: false,
            type: String | Number
        }
    },
    methods: {
        concat(text, punct = ".") {
            if (this.hasSuframa) {
                punct = "";
                this.hasSuframa = false;
            }

            var str = $.trim(this.descCompText.valueOf());
            text = $.trim(text);
            str = str.replace(/(.+?)([.,])+$/g, "$1");
            if (text.length > 0) {
                var first = str.length > 0 ? punct + " " : "";
                this.descCompText = str.concat(first, text);
            } else {
                this.descCompText = str;
            }
        },
        isValidField(value) {
            return (
                value != "undefined" &&
                value != undefined &&
                value &&
                value != ""
            ) ? true : false;
        },
        getLiSelector() {
            return $('#aba-li input[type="radio"].li-checked:checked');
        },
        getLiValues() {
            var checkLi = this.getLiSelector();
            if (checkLi.length == 1) {
                return {
                    orgao_anuente: checkLi.attr("data-orgao-anuente"),
                    destaque: checkLi.attr("data-destaque"),
                    li: "SIM",
                };
            }

            return false;
        },
        hasLiFun() {
            console.log($("#aba-li input.li-checked").length);
            return $("#aba-li input.li-checked").length > 0 ? true : false;
        },
        getNveValues() {
            var values = [];
            $("#aba-nve select.nve-selected").each(function (e) {
                values.push({
                    name: $(this).attr("name"),
                    value: $(this).val(),
                });
            });

            return values;
        },
        hasNveFun() {
            return $("#aba-nve select.nve-selected").length > 0 ? true : false;
        },
        getCampos() {
            return this.$http.get('cadastros/empresa/get_campos_concat/'+this.id_empresa);
        },
        mountValues() {
            this.descricao = $("textarea#descricao").val();
            this.descResumida = $("textarea#descricao_mercado_local").val();
            this.descCompleta = $("textarea#descricao_proposta_completa").val();
            this.subsidio = $("textarea#subsidio").val();
            this.funcao = $("textarea#funcao").val();
            this.aplicacao = $("textarea#aplicacao").val();
            this.materialConstitutivo = $("textarea#material_constitutivo").val();
            this.marca = $("input#marca").val();
            this.hasLi = this.hasLiFun();
            this.addedLi = this.getLiValues();
            this.hasNve = this.hasNveFun();
            this.addedNve = this.getNveValues();
        },
        descricaoConcat(){
            if (this.isValidField(this.descricao)) {
                this.concat(this.descricao);
            }
        },
        descResumidaConcat(){},
        descCompletaConcat(){},
        subsidioConcat(){
            if (this.isValidField(this.subsidio)) {
                this.concat(this.subsidio, ",");
            }
        },
        suframaDescricaoConcat(){
            var destaqueElement = $("input[name='destaque']:checked");

            if (destaqueElement.length) {
                var destaqueTd = destaqueElement.parent().parent();
                var suframaDescricaoHtml = destaqueTd.find(".suframa-descricao").html();
                if (suframaDescricaoHtml) {
                    this.suframaDescricao = destaqueTd.find(".suframa-descricao").html() + " **SUFRAMA** ";
                }
            }

            if (this.isValidField(this.suframaDescricao)) {
                this.concat(this.suframaDescricao, "");
                this.hasSuframa = true;
            }
        },
        destaqueElementConcat(){},
        funcaoConcat(){
            if (this.isValidField(this.funcao)) {
                this.concat("FUNCAO: " + this.funcao);
            }
        },
        aplicacaoConcat(){
            if (this.isValidField(this.aplicacao)) {
                this.concat("APLICACAO: " + this.aplicacao);
            }
        },
        materialConstitutivoConcat(){
             if (this.isValidField(this.materialConstitutivo)) {
                this.concat("MATERIAL CONSTITUTIVO: " + this.materialConstitutivo);
            }
        },
        marcaConcat(){
            if (this.isValidField(this.marca)) {
                this.concat("MARCA: " + this.marca);
            }
        },
        nveConcat(){
            if (this.hasNve) {
                if (this.addedNve.length > 0) {
                    var valid_nve = this.addedNve.find((element) => element.value != null);
                    var last = this.addedNve.filter((item) => {
                        return item.value != undefined && item.value;
                    });
                    if (valid_nve != undefined && valid_nve) {
                        var desc_atributos_nve = "ATRIBUTOS NVE: ";
                        for (let i = 0; i < this.addedNve.length; i++) {
                            const element = this.addedNve[i];
                            if (element.value != undefined && element.value) {
                                desc_atributos_nve += element.name + " ";
                                if (i == last.length - 1) {
                                    desc_atributos_nve += element.value + " ";
                                } else {
                                    desc_atributos_nve += element.value + "; ";
                                }
                            }
                        }

                        this.concat(desc_atributos_nve);
                    }
                }
            }
        },
        liConcat(){
            if (this.hasLi) {
                if (this.addedLi && this.addedLi.destaque && this.addedLi.destaque != null) {
                    this.concat(
                        "DESTAQUE: " + this.addedLi.destaque,
                    );
                }
            }
        },
        codFornecedorConcat() {
            var part_numbers = $(
                '#table-itens .item_selected[type="checkbox"]:checked'
            );

            if (part_numbers.length > 0) {
                var first_pn = part_numbers.first();                   

                this.concat(" COD. FORNECEDOR: " + first_pn.attr("data-part-number"));
            }
        },
        codImportadorConcat() {
            var part_numbers = $(
                '#table-itens .item_selected[type="checkbox"]:checked'
            );

            if (part_numbers.length > 0) {
                var first_pn = part_numbers.first();                   

                this.concat(" COD. IMPORTADOR: " + first_pn.attr("data-part-number"));
            }
        },
        callEvt(campo) {
            switch (campo) {
                case 'subsidio':
                    this.subsidioConcat();
                    break;

                case 'suframa':
                    this.suframaDescricaoConcat();
                    break;

                case 'funcao':
                    this.funcaoConcat();
                    break;

                case 'aplicacao':
                    this.aplicacaoConcat();
                    break;

                case 'material_constitutivo':
                    this.materialConstitutivoConcat();
                    break;

                case 'marca':
                    this.marcaConcat();
                    break;

                case 'nve':
                    this.nveConcat();
                    break;

                case 'li':
                    this.liConcat();
                    break;

                case 'cod_importador':
                    this.codImportadorConcat();
                    break;

                case 'cod_fornecedor':
                    this.codFornecedorConcat();
                    break;

                case 'descricao':
                    this.descricaoConcat();
                    break;
            
                default:
                    break;
            }
        },
        async handleClick() {
            this.mountValues();

            var { data } = await this.getCampos();
            
            this.campos = data;

            $("#concatenar_campos").val(1);

            this.descCompText = "";

            for (let i = 0; i < this.campos.length; i++) {
                const element = this.campos[i];
                if (element.checked) {
                    await this.callEvt(element.slug);
                }
            }

            //copia o campo descrição para descrição resumida
            if (
                this.isValidField(this.descricao) && 
                (this.preencher_descricao_resumida == '0' || this.preencher_descricao_resumida == 0)
            ) {
                $("textarea#descricao_mercado_local").val(this.descricao);
            }

            $("textarea#descricao_proposta_completa").val(this.descCompText);
        }
    },
    mounted() {
        
    }
}
</script>